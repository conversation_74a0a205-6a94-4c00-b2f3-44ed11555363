#pragma once

#include "../BaseObject/PlayObject.h"
#include "../Protocol/NetworkManager.h"
#include "../Protocol/MessageConverter.h"
#include <unordered_map>
#include <vector>
#include <mutex>
#include <shared_mutex>
#include <memory>
#include <functional>

namespace MirServer {

// 前向声明
class Environment;
class MapManager;
class ItemManager;
class MagicManager;
class StorageManager;
class TradeManager;
class QuestManager;
class MiniMapManager;
class RepairManager;
class PKManager;
class GroupManager;
class GuildManager;
class CastleManager;

// 用户引擎类（对应delphi的TUserEngine）
class UserEngine {
public:
    UserEngine();
    ~UserEngine();

    // 初始化和清理
    bool Initialize(std::shared_ptr<MapManager> mapManager,
                   std::shared_ptr<ItemManager> itemManager,
                   std::shared_ptr<MagicManager> magicManager = nullptr,
                   std::shared_ptr<StorageManager> storageManager = nullptr,
                   std::shared_ptr<TradeManager> tradeManager = nullptr,
                   std::shared_ptr<QuestManager> questManager = nullptr,
                   std::shared_ptr<MiniMapManager> miniMapManager = nullptr,
                   std::shared_ptr<RepairManager> repairManager = nullptr);
    void Finalize();

    // 玩家管理
    std::shared_ptr<PlayObject> CreatePlayer(const HumDataInfo& humData);
    bool AddPlayer(std::shared_ptr<PlayObject> player);
    bool RemovePlayer(const std::string& charName);
    std::shared_ptr<PlayObject> GetPlayer(const std::string& charName) const;
    std::shared_ptr<PlayObject> GetPlayerByConnection(uint32_t connectionId) const;
    std::vector<std::shared_ptr<PlayObject>> GetAllPlayers() const;
    size_t GetPlayerCount() const;

    // 玩家查找
    std::shared_ptr<PlayObject> FindPlayer(std::function<bool(const std::shared_ptr<PlayObject>&)> predicate) const;
    std::vector<std::shared_ptr<PlayObject>> FindPlayers(std::function<bool(const std::shared_ptr<PlayObject>&)> predicate) const;

    // 会话管理
    bool BindPlayerToConnection(std::shared_ptr<PlayObject> player, uint32_t connectionId);
    void UnbindPlayerConnection(uint32_t connectionId);

    // 玩家登录/登出
    bool PlayerLogin(std::shared_ptr<PlayObject> player);
    bool PlayerLogout(const std::string& charName);
    void PlayerDisconnect(uint32_t connectionId);

    // 广播消息
    void BroadcastMessage(const std::string& message, BYTE color = 0);
    void BroadcastSystemMessage(const std::string& message);
    void BroadcastPacket(const std::vector<uint8_t>& packet);
    void BroadcastPacketInRange(const Point& center, int range, const std::string& mapName, const std::vector<uint8_t>& packet);

    // 运行时更新
    void Run();
    void ProcessPlayers();
    void CheckPlayerTimeOut();
    void SaveAllPlayers();

    // 统计信息
    struct Statistics {
        size_t totalPlayers = 0;
        size_t activePlayers = 0;
        size_t maxConcurrentPlayers = 0;
        DWORD totalLoginCount = 0;
        DWORD totalLogoutCount = 0;
    };
    Statistics GetStatistics() const;

    // GM命令处理
    bool ProcessGMCommand(std::shared_ptr<PlayObject> player, const std::string& command);

    // 事件处理
    using PlayerEventHandler = std::function<void(std::shared_ptr<PlayObject>)>;
    void SetOnPlayerLogin(PlayerEventHandler handler) { m_onPlayerLogin = handler; }
    void SetOnPlayerLogout(PlayerEventHandler handler) { m_onPlayerLogout = handler; }
    void SetOnPlayerDeath(PlayerEventHandler handler) { m_onPlayerDeath = handler; }
    void SetOnPlayerLevelUp(PlayerEventHandler handler) { m_onPlayerLevelUp = handler; }

    // 排行榜
    std::vector<std::shared_ptr<PlayObject>> GetTopLevelPlayers(size_t count) const;
    std::vector<std::shared_ptr<PlayObject>> GetTopPKPlayers(size_t count) const;

    // 系统功能
    void KickPlayer(const std::string& charName, const std::string& reason = "");
    void KickAllPlayers(const std::string& reason = "");
    bool IsPlayerOnline(const std::string& charName) const;

    // 定时任务
    void RegisterTimerTask(const std::string& name, std::function<void()> task, DWORD interval);
    void UnregisterTimerTask(const std::string& name);

private:
    // 内部方法
    void RegisterEventHandlers();
    void ProcessPlayerPackets(std::shared_ptr<PlayObject> player);
    void HandlePlayerMovement(std::shared_ptr<PlayObject> player);
    void HandlePlayerCombat(std::shared_ptr<PlayObject> player);
    void HandlePlayerItems(std::shared_ptr<PlayObject> player);
    void HandlePlayerTrade(std::shared_ptr<PlayObject> player);
    void HandlePlayerGroup(std::shared_ptr<PlayObject> player);
    void HandlePlayerGuild(std::shared_ptr<PlayObject> player);
    void HandlePlayerMagic(std::shared_ptr<PlayObject> player);
    void HandlePlayerQuest(std::shared_ptr<PlayObject> player);
    void HandlePlayerStorage(std::shared_ptr<PlayObject> player);
    void HandlePlayerRepair(std::shared_ptr<PlayObject> player);
    void UpdatePlayerEnvironment(std::shared_ptr<PlayObject> player);
    void SendAroundObjects(std::shared_ptr<PlayObject> player);

    // GM命令实现
    void GMCommand_Level(std::shared_ptr<PlayObject> player, const std::vector<std::string>& args);
    void GMCommand_Gold(std::shared_ptr<PlayObject> player, const std::vector<std::string>& args);
    void GMCommand_Item(std::shared_ptr<PlayObject> player, const std::vector<std::string>& args);
    void GMCommand_Move(std::shared_ptr<PlayObject> player, const std::vector<std::string>& args);
    void GMCommand_Kick(std::shared_ptr<PlayObject> player, const std::vector<std::string>& args);
    void GMCommand_Shutdown(std::shared_ptr<PlayObject> player, const std::vector<std::string>& args);

private:
    // 玩家列表
    std::unordered_map<std::string, std::shared_ptr<PlayObject>> m_players; // charName -> player
    std::unordered_map<uint32_t, std::shared_ptr<PlayObject>> m_connectionMap; // connectionId -> player
    mutable std::shared_mutex m_playersMutex;

    // 依赖的其他管理器
    std::shared_ptr<MapManager> m_mapManager;
    std::shared_ptr<ItemManager> m_itemManager;
    std::shared_ptr<MagicManager> m_magicManager;
    std::shared_ptr<StorageManager> m_storageManager;
    std::shared_ptr<TradeManager> m_tradeManager;
    std::shared_ptr<QuestManager> m_questManager;
    std::shared_ptr<MiniMapManager> m_miniMapManager;
    std::shared_ptr<RepairManager> m_repairManager;

    // 统计信息
    Statistics m_statistics;
    mutable std::mutex m_statsMutex;

    // 事件处理器
    PlayerEventHandler m_onPlayerLogin;
    PlayerEventHandler m_onPlayerLogout;
    PlayerEventHandler m_onPlayerDeath;
    PlayerEventHandler m_onPlayerLevelUp;

    // 定时任务
    struct TimerTask {
        std::string name;
        std::function<void()> task;
        DWORD interval;
        DWORD lastRunTime;
    };
    std::vector<TimerTask> m_timerTasks;
    std::mutex m_timerMutex;

    // 运行状态
    bool m_initialized = false;
    DWORD m_lastSaveTime = 0;
    DWORD m_lastCheckTime = 0;

    // 配置
    DWORD m_autoSaveInterval = 300000; // 5分钟自动保存
    DWORD m_timeoutCheckInterval = 60000; // 1分钟检查超时
    DWORD m_playerTimeout = 180000; // 3分钟无响应超时
};

// 全局用户引擎实例
extern std::unique_ptr<UserEngine> g_UserEngine;

} // namespace MirServer